{"PravniAkt": {"source_file": "F:\\nocsv\\jsonld\\002PravniAkt.jsonld", "output_file": "pravni_akt.csv", "label": "PravniAkt", "id_space": "PravniAkt-ID", "unique_id_source_key": "iri", "properties": {"aktCisloPredpisu": "Integer", "aktRokPredpisu": "String", "aktSbirkaKod": "String", "aktCitace": "String", "aktNazevVyhlaseny": "String", "aktKod": "String", "zneniBaseId": "Integer"}, "source_json_key_mapping": {"aktCisloPredpisu": "akt-číslo-předpisu", "aktRokPredpisu": "akt-rok-předpisu", "aktSbirkaKod": "akt-sbírka-kód", "aktCitace": "akt-citace", "aktNazevVyhlaseny": "akt-název-vyhlášený", "aktKod": "akt-kód", "zneniBaseId": "znění-base-id"}}, "PravniAktZneni": {"source_files": ["F:\\nocsv\\jsonld\\001PravniAktZneni\\part_1.jsonld", "F:\\nocsv\\jsonld\\001PravniAktZneni\\part_2.jsonld", "F:\\nocsv\\jsonld\\001PravniAktZneni\\part_3.jsonld", "F:\\nocsv\\jsonld\\001PravniAktZneni\\part_4.jsonld", "F:\\nocsv\\jsonld\\001PravniAktZneni\\part_5.jsonld", "F:\\nocsv\\jsonld\\001PravniAktZneni\\part_6.jsonld"], "output_file": "pravni_akt_zneni.csv", "label": "PravniAktZneni", "id_space": "PravniAktZneni-ID", "unique_id_source_key": "iri", "properties": {"aktCitace": "String", "aktNazevVyhlaseny": "String", "zneniId": "Integer", "zneniKod": "String", "zneniDokumentId": "Integer", "zneniBalicekPublikaceId": "Integer", "zneniDatumCasPosledniZmeny": "DateTime", "zneniBaseId": "Integer", "zneniDatumUcinnostiOd": "Date", "zneniDatumUcinnostiDo": "Date", "zneniEli": "String", "zneniRocnik": "Integer", "metadataNazevCitace": "String", "metadataDatumUcinnostiOd": "Date", "metadataDatumZruseni": "Date"}, "source_json_key_mapping": {"aktCitace": "akt-citace", "aktNazevVyhlaseny": "akt-název-vyhlášený", "zneniId": "znění-id", "zneniKod": "znění-kód", "zneniDokumentId": "znění-dokument-id", "zneniBalicekPublikaceId": "znění-balíček-publikace-id", "zneniDatumCasPosledniZmeny": "znění-datum-čas-poslední-změny", "zneniBaseId": "znění-base-id", "zneniDatumUcinnostiOd": "znění-datum-účinnosti-od", "zneniDatumUcinnostiDo": "znění-datum-účinnosti-do", "zneniEli": "znění-eli", "zneniRocnik": "znění-ročník", "metadataNazevCitace": "metadata-název-citace", "metadataDatumUcinnostiOd": "metadata-datum-účinnosti-od", "metadataDatumZruseni": "metadata-datum-zrušení"}}, "Vztah_Zneni_Akt": {"source_files": ["F:\\nocsv\\jsonld\\001PravniAktZneni\\part_1.jsonld", "F:\\nocsv\\jsonld\\001PravniAktZneni\\part_2.jsonld", "F:\\nocsv\\jsonld\\001PravniAktZneni\\part_3.jsonld", "F:\\nocsv\\jsonld\\001PravniAktZneni\\part_4.jsonld", "F:\\nocsv\\jsonld\\001PravniAktZneni\\part_5.jsonld", "F:\\nocsv\\jsonld\\001PravniAktZneni\\part_6.jsonld"], "output_file": "vztah_zneni_akt.csv", "relationship_type": "JE_ZNENIM_AKTU", "start_node": {"id_space": "PravniAktZneni-ID", "source_key": "iri"}, "end_node": {"id_space": "PravniAkt-ID", "source_key": "akt-iri"}, "properties": {}}, "Vztah_Rocnik_Akt": {"source_file": "F:\\nocsv\\jsonld\\011PravniAktRocnikSbirky.jsonld", "output_file": "vztah_rocnik_akt.csv", "relationship_type": "OBSAHUJE_AKT", "start_node": {"id_space": "PravniAktRocnik-ID", "source_key": "iri"}, "end_node": {"id_space": "PravniAkt-ID", "source_key": "iri"}, "properties": {"datumCasPosledniZmeny": "DateTime"}, "source_json_key_mapping": {"datumCasPosledniZmeny": "datum-čas-poslední-změny"}}, "PravniAktZneniFragment": {"source_files": ["F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_1.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_2.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_3.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_4.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_5.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_6.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_7.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_8.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_9.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_10.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_11.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_12.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_13.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_14.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_15.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_16.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_17.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_18.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_19.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_20.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_21.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_22.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_23.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_24.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_25.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_26.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_27.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_28.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_29.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_30.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_31.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_32.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_33.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_34.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_35.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_36.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_37.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_38.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_39.jsonld", "F:\\nocsv\\jsonld\\003PravniAktZneniFragment\\part_40.jsonld"], "output_file": "pravni_akt_zneni_fragment.csv", "label": "PravniAktZneniFragment", "id_space": "PravniAktZneniFragment-ID", "unique_id_source_key": "iri", "properties": {"fragmentId": "Integer", "fragmentKod": "String", "fragmentNazev": "String", "fragmentObsah": "String", "fragmentTyp": "String", "fragmentCislo": "String", "zneniId": "Integer"}, "source_json_key_mapping": {"fragmentId": "fragment-id", "fragmentKod": "fragment-kód", "fragmentNazev": "fragment-n<PERSON><PERSON>v", "fragmentObsah": "fragment-obsah", "fragmentTyp": "fragment-typ", "fragmentCislo": "fragment-<PERSON><PERSON><PERSON>", "zneniId": "znění-id"}}, "PravniAktFragment": {"source_files": ["F:\\nocsv\\jsonld\\004PravniAktFragment\\part_1.jsonld", "F:\\nocsv\\jsonld\\004PravniAktFragment\\part_2.jsonld", "F:\\nocsv\\jsonld\\004PravniAktFragment\\part_3.jsonld", "F:\\nocsv\\jsonld\\004PravniAktFragment\\part_4.jsonld", "F:\\nocsv\\jsonld\\004PravniAktFragment\\part_5.jsonld", "F:\\nocsv\\jsonld\\004PravniAktFragment\\part_6.jsonld", "F:\\nocsv\\jsonld\\004PravniAktFragment\\part_7.jsonld", "F:\\nocsv\\jsonld\\004PravniAktFragment\\part_8.jsonld"], "output_file": "pravni_akt_fragment.csv", "label": "PravniAktFragment", "id_space": "PravniAktFragment-ID", "unique_id_source_key": "iri", "properties": {"fragmentId": "Integer", "fragmentKod": "String", "fragmentNazev": "String", "fragmentObsah": "String", "fragmentTyp": "String", "fragmentCislo": "String", "aktId": "Integer"}, "source_json_key_mapping": {"fragmentId": "fragment-id", "fragmentKod": "fragment-kód", "fragmentNazev": "fragment-n<PERSON><PERSON>v", "fragmentObsah": "fragment-obsah", "fragmentTyp": "fragment-typ", "fragmentCislo": "fragment-<PERSON><PERSON><PERSON>", "aktId": "akt-id"}}, "PravniAktOdkaz": {"source_files": ["F:\\nocsv\\jsonld\\005PravniAktOdkaz\\part_1.jsonld", "F:\\nocsv\\jsonld\\005PravniAktOdkaz\\part_2.jsonld", "F:\\nocsv\\jsonld\\005PravniAktOdkaz\\part_3.jsonld", "F:\\nocsv\\jsonld\\005PravniAktOdkaz\\part_4.jsonld", "F:\\nocsv\\jsonld\\005PravniAktOdkaz\\part_5.jsonld", "F:\\nocsv\\jsonld\\005PravniAktOdkaz\\part_6.jsonld", "F:\\nocsv\\jsonld\\005PravniAktOdkaz\\part_7.jsonld", "F:\\nocsv\\jsonld\\005PravniAktOdkaz\\part_8.jsonld", "F:\\nocsv\\jsonld\\005PravniAktOdkaz\\part_9.jsonld", "F:\\nocsv\\jsonld\\005PravniAktOdkaz\\part_10.jsonld", "F:\\nocsv\\jsonld\\005PravniAktOdkaz\\part_11.jsonld", "F:\\nocsv\\jsonld\\005PravniAktOdkaz\\part_12.jsonld", "F:\\nocsv\\jsonld\\005PravniAktOdkaz\\part_13.jsonld", "F:\\nocsv\\jsonld\\005PravniAktOdkaz\\part_14.jsonld", "F:\\nocsv\\jsonld\\005PravniAktOdkaz\\part_15.jsonld"], "output_file": "pravni_akt_odkaz.csv", "label": "PravniAktOdkaz", "id_space": "PravniAktOdkaz-ID", "unique_id_source_key": "iri", "properties": {"odkazId": "Integer", "odkazTyp": "String", "odkazText": "String", "odkazCil": "String", "fragmentId": "Integer"}, "source_json_key_mapping": {"odkazId": "odkaz-id", "odkazTyp": "odkaz-typ", "odkazText": "odkaz-text", "odkazCil": "odkaz-cíl", "fragmentId": "fragment-id"}}, "CiselnikSbirka": {"source_file": "F:\\nocsv\\jsonld\\012CiselnikSbirka.jsonld", "output_file": "ciselnik_sbirka.csv", "label": "CiselnikSbirka", "id_space": "CiselnikSbirka-ID", "unique_id_source_key": "iri", "properties": {"sbirkaKod": "String", "sbirkaNazev": "String", "sbirkaZkratka": "String"}, "source_json_key_mapping": {"sbirkaKod": "sbírka-kód", "sbirkaNazev": "sbírka-název", "sbirkaZkratka": "sbírka-zkratka"}}, "CiselnikTypFragmentu": {"source_file": "F:\\nocsv\\jsonld\\013CiselnikTypFragmentu.jsonld", "output_file": "ciselnik_typ_fragmentu.csv", "label": "CiselnikTypFragmentu", "id_space": "CiselnikTypFragmentu-ID", "unique_id_source_key": "iri", "properties": {"typKod": "String", "typNazev": "String", "typPopis": "String"}, "source_json_key_mapping": {"typKod": "typ-kód", "typNazev": "typ-název", "typPopis": "typ-popis"}}, "CiselnikTypOdkazu": {"source_file": "F:\\nocsv\\jsonld\\014CiselnikTypOdkazu.jsonld", "output_file": "ciselnik_typ_odkazu.csv", "label": "CiselnikTypOdkazu", "id_space": "CiselnikTypOdkazu-ID", "unique_id_source_key": "iri", "properties": {"typKod": "String", "typNazev": "String", "typPopis": "String"}, "source_json_key_mapping": {"typKod": "typ-kód", "typNazev": "typ-název", "typPopis": "typ-popis"}}, "PravniAktMetadata": {"source_file": "F:\\nocsv\\jsonld\\006PravniAktMetadata.jsonld", "output_file": "pravni_akt_metadata.csv", "label": "PravniAktMetadata", "id_space": "PravniAktMetadata-ID", "unique_id_source_key": "iri", "properties": {"metadataId": "Integer", "metadataKluc": "String", "metadataHodnota": "String", "aktId": "Integer"}, "source_json_key_mapping": {"metadataId": "metadata-id", "metadataKluc": "metadata-klíč", "metadataHodnota": "metadata-hodnota", "aktId": "akt-id"}}, "PravniAktKomentarFragmentu": {"source_file": "F:\\nocsv\\jsonld\\007PravniAktKomentarFragmentu.jsonld", "output_file": "pravni_akt_komentar_fragmentu.csv", "label": "PravniAktKomentarFragmentu", "id_space": "PravniAktKomentarFragmentu-ID", "unique_id_source_key": "iri", "properties": {"komentarId": "Integer", "komentarText": "String", "komentarAutor": "String", "komentarDatum": "Date", "fragmentId": "Integer"}, "source_json_key_mapping": {"komentarId": "komentář-id", "komentarText": "komentář-text", "komentarAutor": "koment<PERSON><PERSON>-autor", "komentarDatum": "komentář-datum", "fragmentId": "fragment-id"}}, "neo4j_type_mapping": {"Integer": "int", "String": "string", "Date": "date", "DateTime": "datetime", "Boolean": "boolean"}}